Title: Noti Backend Refactor: Three-Phase Analysis, Plan, and Implementation Strategy

Version: 1.0
Date: 2025-08-02
Scope: Electron main-process backend (electron/main/**/*, preload bridge), database layer (better-sqlite3), sync/backup engine, notes/books/media/timer/settings IPC APIs

Executive Summary
The current backend is functionally rich but exhibits monolithic tendencies: large IPC handler modules multiplexing many responsibilities, tightly-coupled database primitives mixed into business logic, and duplication of concerns across APIs. A migration to better-sqlite3 is underway and appears complete for core flows. A unified sync/backup engine based on a manifest file is in place and relatively robust.

This document delivers:
1) Phase 1: An in-depth architecture assessment based on full-file analysis.
2) Phase 2: A research-backed modular target architecture, tooling choices, and a PRD with risk, dependencies, and success criteria.
3) Phase 3: A pragmatic, low-risk migration strategy and timeline that preserves behavior while enabling rapid future delivery.

PHASE 1 — Deep Codebase Understanding

1.1 Backend Inventory and Responsibilities
Core entrypoints
- electron/main/index.ts
  - App lifecycle, BrowserWindow creation, protocol registration, menu control
  - Boot pipeline: initializeProtocolHandlers, initializeIpcHandlers (which internally init DB + sync), then createWindow
  - Startup auto-sync trigger if autoSyncEnabled and syncDirectory set
  - Before-quit cleanup: end active timer session, discord RPC shutdown, closeDatabase
- electron/main/ipc-handlers.ts
  - Initializes DB once (initDatabase) and then initializes syncAPI
  - Central IPC registration for domains:
    - Notes, Folders, RecentItems, Settings (+Theme), Timer (sessions, cycles, settings), Media, Books (including OpenLibrary integration & cover mgmt), Discord RPC, Sync, DatabaseHooks, Dialog utilities
  - Handles transient validation/errors, but delegates domain logic to ./api/* modules

Protocols and preload
- protocol-handlers.ts
  - noti-media:// custom protocol to safely serve local files with proper decoding and Windows path semantics
- electron/preload/api-bridge.ts (not analyzed in this pass; serves renderer IPC bridge)

Database Layer
- database.ts
  - Better-sqlite3 connection, PRAGMA config, schema creation, migrations for columns/indexes, default data (Books root), and lifecycle (close)
  - Tables: notes, folders, books, recent_items, theme_settings, exports, search_history, settings, media_files, timer_sessions, pomodoro_cycles, timer_settings, sync_state, sync_sessions, sync_directory_state
- database-api.ts
  - Synchronous CRUD helpers: dbGet, dbAll, dbRun (wrapping better-sqlite3)
  - Entity CRUD: Notes, Folders, Books; plus transaction helpers (withTransaction, withReadTransaction) and caching (prepareCached)
  - Backup utilities (native backup), WAL monitoring helpers
  - deleteBook performs DB+filesystem cleanup of media_files
- database-hooks.ts
  - Central event-emitter that records changes (create/update/delete) for note/folder/book and integrates with auto-sync system
  - Maintains an in-memory change history and pending deletions queue

Domain APIs (electron/main/api)
- notes-api.ts
  - Validation wrappers around database-api; enrichment (NoteWithMetadata)
  - Export/import (.pdf, .md, .noti), multi-export zip, puppeteer-based PDF, embedded fonts logic, media URL replacement
  - Folder traversal for export
  - Book-note linkage helpers and auto-linking
  - Emits database hooks for create/update/delete
- folders-api.ts
  - Validation, hierarchical operations, Books root enforcement, circular checks
  - Presents hierarchy with child counts; supports delete with note move or cascade
  - Ensures backward compatibility with legacy book folders
  - Emits database hooks
- books-api.ts
  - OpenLibrary search (online), ISBN lookup, details retrieval (works/editions/authors), caching
  - Cover download pipeline with redirects and timeouts; saves cover into media_files via media-api, then nulls cover_url to avoid manifest bloat
  - Book folder management under “Books” root; ensureFoldersForAllBooks
  - deleteBookAndHandleFolder: unlink/move notes, remove folder if empty, then delete book
  - Emits database hooks
- media-api.ts
  - Media storage path in userData/media; filePathToMediaUrl; CRUD for media_files and disk files; cover management
- settings-api.ts
  - JSON-serialized value storage in settings table; theme_settings management; transaction for setActiveTheme

Sync/Backup System (electron/main/api/sync-logic)
- unified-sync-engine.ts
  - Orchestrates bidirectional sync with a manifest (.sync-manifest.json)
  - Flow: set sync dir -> load or initialize manifest -> record pending deletions (with real file deletion) -> ChangeDetector.compareStates -> import (books → folders → notes) -> export (with renames) -> conflict resolve -> deletions -> cleanup renames -> update manifest -> update sync state (sync_directory_state)
  - Ensures relationships by mapping manifest IDs to local DB IDs during import; embeds media in .noti
  - Performs cautious filesystem ops via file-operations with validation
- change-detector.ts
  - Computes toImport/toExport/conflicts/toDelete comparing DB state vs manifest; hashes DB items; uses sync_state table for last hashes
- manifest-manager.ts
  - Loads/saves manifest, generates from DB, name sanitization with collision prevention, relationship embedding, performs initial sync creation, calculates manifest hash, merges manifests
- file-operations.ts
  - Validated filesystem operations within sync directory; atomic writes, hidden attribute on Windows; robust rename with cross-device fallback
- types.ts
  - Comprehensive typing for sync and .noti structure

1.2 Architecture and Data Flow
High-level flow
- Renderer -> preload bridge -> ipcMain handlers (ipc-handlers.ts) -> domain API modules -> database-api for CRUD and domain logic
- Media URLs are bridged via noti-media:// to keep webSecurity enabled
- Sync system integrates with databaseHooks for change capture; on startup or manual invoke, unifiedSyncEngine performs cycle; manifest is source-of-truth for backup structure

Data ownership and coupling
- Many domain modules directly import database-api CRUD (tight coupling to SQL layer)
- ipc-handlers.ts accumulates numerous handlers: acts as a “god module” registering all flows
- Some business logic duplicated:
  - Books API handles folder creation and also ensureFoldersForAllBooks covers similar paths
  - Notes export logic intertwines renderer-ish concerns (PDF via puppeteer, HTML/CSS embedding)
- Settings and sync use settings-api; sync writes its own sync_directory_state

Error handling and validation
- Reasonable validation in APIs (inputs, sizes, types)
- Many try/catches with logging; consistent use of ISO timestamps

Performance and reliability
- better-sqlite3 with optimized pragmas (WAL, cache_size, page_size, mmap_size, NORMAL sync)
- Statement cache exists but limited
- Index coverage added; includes important new indexes (e.g., idx_media_files_book_cover, idx_books_title)
- Sync uses hashing and manifest; computes renames and handles cross-device renames

Security
- webSecurity on; noti-media:// protocol validates path and prevents traversal
- file-operations.ts validates paths within sync directory

1.3 Pain Points and Bottlenecks
- Monolithic ipc-handlers.ts: central registration blends orchestration concerns, making evolvability slow
- Domain APIs tightly coupled to database-api (SQL strings embedded in database-api, called across modules). Hard to adopt Kysely without touching many sites.
- notes-api.ts overly large: validation, export/import, PDF generation, embedded font logic, media processing—all in one module (SRP violation)
- Sync engine complexity spread across unified-sync-engine, manifest-manager, change-detector, file-operations. Good separation, but change-detector and manifest-manager both traverse DB and transformations; could consolidate contracts and caching to reduce repeated queries.
- File and DB paths: Logic around Books folder, legacy names, migration checks sprinkled across folders/books APIs.
- Testing difficulty: Business logic not isolated from IO (DB, FS, network). Hard to unit test without integration environment.
- Lack of clear domain boundaries: Notes, Books, Folders partially cross-reference each other’s invariants (e.g., auto-linking, folder move rules) via imports rather than via clearly defined services.

1.4 Current Migration Context
- Using better-sqlite3 consistently; pragmas and schema creation settled
- Sync system moved to manifest-only tracking, with sync_state retained for hashes
- Media storage offloads covers to files; cover_url nulled to reduce manifest bloat

1.5 Component Relationships
- ipc-handlers.ts orchestrates all IPC signatures and delegates to:
  - notes-api, folders-api, books-api, settings-api, timer-api (not examined fully here), media-api
  - syncAPI (wrapper exposing unifiedSyncEngine/manifest operations)
  - databaseHooks history endpoints
- Domain APIs -> database-api
- Sync logic -> database-api + file-operations + manifest-manager; listens to databaseHooks for pending deletions

PHASE 2 — Strategic Architecture Planning

2.1 Target Architecture Overview
Goals
- Modularize by domain: notes, books, folders, media, timer, settings, sync as independent services with clear interfaces
- Introduce a Repository + Service pattern: repositories encapsulate DB (and later Kysely), services contain business logic and refer only to repositories via interfaces
- Introduce an IPC Router layer: per-domain router registering IPC handlers against service interfaces
- Extract cross-cutting utilities: validation, file paths, ID mapping, date/format helpers
- Isolate side effects: network calls (OpenLibrary), filesystem writes, and DB in adapters; pure business logic testable

Proposed layering
- electron/main/core/
  - app.ts (bootstrapping, DI container)
  - ipc/
    - router.ts (register handlers)
    - routes/{notes,books,folders,media,timer,settings,sync}.router.ts
  - services/
    - {notes,books,folders,media,timer,settings,sync}.service.ts
    - validation and orchestration logic
  - repositories/
    - {notes,books,folders,media,timer,settings,recents}.repo.ts
    - encapsulate DB access
  - db/
    - client.ts (better-sqlite3 connection holder)
    - schema.sql.ts or migrator
    - query layer (Kysely optional)
  - adapters/
    - http/openlibrary.client.ts (axios + retries)
    - fs/media.store.ts (media read/write)
    - sync/{manifest.manager.ts, change.detector.ts, file.ops.ts} reorganized with smaller cohesive units
  - events/
    - hooks.ts (database-hooks functionality abstracted as DomainEvents)
- electron/main/index.ts becomes minimal bootstrap
- electron/main/ipc-handlers.ts split into routers per domain
- Use DI (tsyringe or typedi) to wire services and repositories

2.2 Library and Pattern Recommendations
Database layer improvements
- Kysely with better-sqlite3 dialect to replace handwritten SQL:
  - Pros: Typed queries, composability, safer migrations, reduced stringly-typed SQL
  - Cons: Migration effort; need to map current SQL usage; performance similar when using better-sqlite3
  - References:
    - Kysely official docs: https://kysely.dev/ (best practices, plugins)
    - better-sqlite3 dialect example: https://github.com/kysely-org/kysely/blob/master/site/docs/dialects.md
  - Strategy: Incrementally introduce Kysely repositories while keeping old database-api for legacy paths; dual-run during transition for critical reads if needed

File system operations
- Current file-operations.ts is good. Consider:
  - fs-extra for convenience (already used in some places) to simplify ensure/copy/remove
  - globby for structured file scans where needed
  - References:
    - fs-extra: https://github.com/jprichardson/node-fs-extra
    - Node.js fs Promises: https://nodejs.org/api/fs.html#fs_file_system

HTTP/Network
- axios with retry/backoff via axios-retry for OpenLibrary robustness
  - axios-retry: https://github.com/softonic/axios-retry
  - OpenLibrary API: https://openlibrary.org/dev/docs/api/books and https://openlibrary.org/dev/docs/api/search

Validation
- zod or class-validator for IPC payload validation at router boundary
  - zod: https://github.com/colinhacks/zod
  - Benefit: consistent, testable, schemas reused across renderer if needed

Timers/state management
- Extract timer logic into its own service with a state machine for sessions and pomodoro cycles
  - XState could be used if complexity warrants: https://xstate.js.org/
  - Otherwise a lightweight finite-state pattern is sufficient

Backup/Sync
- Keep manifest-based approach; modularize responsibilities:
  - ChangeDetector: depends on Repository DAL projections (not raw SQL)
  - ManifestManager: pure transformations + IO via file ops adapter
  - UnifiedSyncEngine: orchestrator only; emit structured events
  - Consider JSON schema for .noti (ajv) to validate files
    - ajv: https://ajv.js.org/
- Add metrics and tracing (counts, timings) to measure sync performance

Logging/Observability
- pino for structured logs in main process
  - https://github.com/pinojs/pino
- Centralized error codes and context

2.3 PRD — Refactor and Modernization
Objective
- Transform backend into modular, testable, maintainable system that accelerates feature delivery and reduces regression risk.

Non-goals
- UI/Renderer refactor
- Changing data model semantics or breaking storage formats

Functional Requirements
- Preserve all existing functionality: Notes CRUD, export/import, Books with OpenLibrary integration and covers, Media, Timer sessions and cycles, Settings & Themes, Sync/backup engine with manifest and deletions
- Maintain IPC contracts initially; routers will register identical channel names

Technical Requirements
- Introduce service/repository layers; repositories become the sole DB access points
- Optionally integrate Kysely for new repositories
- Introduce validation layer (zod) at IPC boundaries
- Add DI container to wire modules
- Extract OpenLibrary HTTP client with retries/timeouts
- Normalize file/media path handling utilities
- Modularize sync components and centralize schema/types

Task Breakdown with Estimates (S/M/L/XL represent complexity)
1) Scaffolding and DI
   - Create core structure, DI container, base router pattern (S)
   - Migrate init pipeline in main/index.ts to use bootstrap (S)
2) Settings + Themes module as pilot
   - Create settings.repository (Kysely or current db-api), settings.service, settings.router (M)
   - Wire theme operations and transactional setActiveTheme (S)
   - Tests for service logic (S)
3) Notes module
   - notes.repository (queries for note projections used by sync/export) (M)
   - notes.service separating validation, search, and export/import hooks (L)
   - Split export code: move PDF/HTML/media embedding into adapters/notes-exporter (L)
   - notes.router (S)
4) Folders module
   - folders.repository (hierarchy, counts) (M)
   - folders.service (validation, circular checks, Books root invariants) (M)
   - folders.router (S)
5) Books module
   - books.repository (book + cover queries) (M)
   - openlibrary.client with retries/backoff; books.service orchestrating folder creation and cover handling (L)
   - books.router (S)
6) Media module
   - media.repository (media_files), media.store adapter for disk (S)
   - media.service simplifies cover CRUD and encapsulates filePathToMediaUrl (S)
   - media.router (S)
7) Timer module
   - timer.repository (sessions, cycles, settings) (M)
   - timer.service (state/flows), optional XState if needed (M/L)
   - timer.router (S)
8) Sync module
   - Refactor unified-sync-engine into smaller services: change-detector.service (consumes repositories), manifest.service, file-ops.service, sync.orchestrator (XL)
   - Introduce domain event bus (replacing databaseHooks usage directly) (M)
   - Schema validation for .noti with ajv (S)
   - sync.router (S)
9) Migrate ipc-handlers.ts to per-domain routers (S)
10) Database client and Kysely integration
   - Introduce db/client.ts and wire repos to it (M)
   - Migrate selected repositories to Kysely first (Settings, Media, Timer), then Notes/Books/Folders incrementally (L/XL)
11) Cross-cutting utilities
   - Validation (zod), logging (pino), error wrappers (S)
   - Filename/language utilities in utilities/ (S)

Dependencies
- Step 1 prerequisite to all routers
- Repositories must exist before services; services before routers
- Sync refactor depends on repositories for projections (Notes/Books/Folders ready first)
- Kysely can be opt-in per repository

Risks and Mitigations
- Risk: IPC contract drift
  - Mitigation: Maintain same channel names and payload shapes initially; add adapter layer if needed
- Risk: Kysely migration touches many queries
  - Mitigation: Incremental per-repo migration; keep database-api as legacy adapter during transition
- Risk: Export/PDF complexity regression
  - Mitigation: Extract to dedicated exporter module with focused tests; preserve existing code paths until parity
- Risk: Sync correctness
  - Mitigation: Add integration tests for import/export/rename/delete flows using temp dirs; add manifest schema validation
- Risk: Time and scope creep
  - Mitigation: Phase gates with acceptance criteria and demos per module

Success Metrics
- 50% reduction in average time to add a backend feature touching one domain (baseline vs post-refactor)
- 30% reduction in IPC-related bugs (measured over 4-8 weeks)
- 70% unit test coverage for services core logic (business rules without IO)
- Sync cycle duration stable or improved; zero data loss incidents in acceptance tests
- Developer satisfaction improved in retro surveys

2.4 Research Citations
- Electron security and custom protocol best practices
  - Electron Security: https://www.electronjs.org/docs/latest/tutorial/security
  - Register custom protocol: https://www.electronjs.org/docs/latest/api/protocol
- Node/Electron architecture patterns
  - Service/Repository and IPC routers are common patterns in large Electron apps (industry practices, examples in open-source apps like VSCode architecture docs)
- Kysely + better-sqlite3
  - Kysely docs: https://kysely.dev/
  - Dialects: https://kysely-org.github.io/kysely-apidoc/interfaces/Dialect.html
- Backup/manifest designs
  - Git-like content hashing and manifest patterns are standard; AJV for JSON schema validation: https://ajv.js.org/
- Timers/state machines
  - XState: https://xstate.js.org/docs/
- HTTP resiliency
  - axios-retry: https://github.com/softonic/axios-retry
- File operations
  - fs-extra: https://github.com/jprichardson/node-fs-extra

PHASE 3 — Implementation Strategy

3.1 Prioritization by Impact and Risk
Tier 1 (High impact, moderate risk)
- Create core scaffolding with DI and IPC routers
- Settings module refactor (pilot): repository + service + router
- Books media/cover pipeline extraction to media.service and openlibrary.client
- Move ipc-handlers.ts into per-domain routers using same channels

Tier 2 (High impact, higher risk)
- Notes extraction: exporter/importer adapters, metadata/service separation
- Folders service and repo with clear invariants (Books root, legacy migration)
- Timer module refactor

Tier 3 (High complexity)
- Sync refactor: split orchestrator, change-detector consuming repositories, manifest manager purity, schema validation
- Kysely migration for Notes/Books/Folders repositories

3.2 Component Isolation Strategies
- Repositories abstract DB; switch implementation from database-api to Kysely without service changes
- Adapters for IO (filesystem, network) behind interfaces; can stub for tests
- IPC routers perform edge validation and call services; services return typed results/errors
- Shared utilities: filename sanitizer, language converter, time formatting, hashing
- DomainEventBus replaces databaseHooks singleton; provides subscription points for sync auto-triggers

3.3 Maintaining Existing Functionality During Refactor
- Preserve IPC channel names and payloads
- Initially, services call through to legacy database-api functions, then swap to repos
- Keep notes exporter code path unchanged until isolated module mirrors behavior; then switch router to service
- Sync orchestrator wrapper exposes the same syncAPI surface; internal pieces can be replaced module-by-module

3.4 Reusable Components and Utilities to Extract
- Validation schemas (zod) for Notes/Books/Folders/Media/Settings/Timer
- OpenLibrary client with retry and language normalization
- Media store for saving/reading covers and note media
- Path utilities and protocol URL helpers
- Date/time and ISO handling
- Error helpers mapping to ErrorCode enums

3.5 Phased Timeline
Phase A (Week 1)
- Core scaffolding: DI, module layout
- Settings module refactor (repo/service/router + tests)
- Convert ipc-handlers.ts: registerSettingsHandlers → settings.router

Phase B (Weeks 2–3)
- Media module (repo/service/router), filePathToMediaUrl centralized
- Books module refactor (repo/service/router), openlibrary.client with retries
- Migrate books cover flow to media.service entirely
- Ensure ensureFoldersForAllBooks logic resides in books.service and folders.service boundary

Phase C (Weeks 4–5)
- Notes module refactor: repo/service/router
- Extract exporter/importer to adapters/notes-exporter with PDF/markdown/.noti logic and media embedding
- Folder module refactor: repo/service/router, hierarchy projections in repo queries

Phase D (Weeks 6–7)
- Timer module: repo/service/router, optional XState
- Basic unit tests for business rules

Phase E (Weeks 8–9)
- Sync refactor: decompose unified-sync-engine; change-detector queries come from repositories
- Manifest schema validation with ajv; conflict resolver kept but isolated
- Add metrics/logging

Phase F (Weeks 10+)
- Kysely migration per-repo starting with Settings/Media, then Timer, then Notes/Books/Folders
- Remove legacy database-api or keep as compatibility layer for a deprecation window

Acceptance and Validation
- Regression runs of core flows (notes CRUD, export, import, books search/add, cover process, folders hierarchy, sync roundtrip, timer sessions)
- Measured metrics vs baseline
- Code reviews ensure module boundaries and test coverage thresholds

Appendix: Specific Observations and Quick Wins
- notes-api.ts size and responsibilities warrant immediate extraction:
  - Exporter module: puppeteer/PDF logic, fonts handling, media URL replacement and base64 embedding; ensure streaming or chunking for large content
  - Importer module: .noti and md/txt parsing; sanitize aggressively; size limits already enforced
- books-api.ts:
  - Move network to openlibrary.client; add axios-retry and uniform headers; isolate timeouts and error mapping
  - All cover flow via media.service; keep DB cover_url clean
- folders-api.ts:
  - Invariants about “Books” root enforced in service; ensure single source of truth for legacy migrations and inherited book_id logic
- database-api.ts:
  - Keep as legacy adapter; new repositories encapsulate SQL with Kysely; aim to remove ad-hoc dbRun calls spread around
- unified-sync-engine.ts:
  - Reduce orchestrator to purely sequencing; dependencies injected (repos, fileOps, manifestSvc, changeDetector, conflictResolver)
  - Decompose cleanupRenamedItems and path-builders into helper services

End of Document
